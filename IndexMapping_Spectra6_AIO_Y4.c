/*
 * IndexMapping_Spectra6_AIO_Y4 - C Implementation
 * Converted from ARM64 assembly code
 * 
 * This function performs color index mapping for Spectra6 AIO display
 * converting RGB888 input to Y4 (4-bit) indexed output
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <stdint.h>
#include <limits.h>

// Forward declarations
void ImageProcess_Spectra6_AIO(unsigned char* src, int width, int height,
                               float param0, float param1, float param2, float param3);

/*
 * ImageProcess_Spectra6_AIO - Simplified implementation
 * This function would normally perform complex image processing
 * For this example, we implement basic color enhancement
 */
void ImageProcess_Spectra6_AIO(unsigned char* src, int width, int height,
                               float param0, float param1, float param2, float param3) {
    int total_pixels = width * height;

    // Use param2 and param3 for additional processing if needed
    (void)param2;  // Suppress unused parameter warning
    (void)param3;  // Suppress unused parameter warning

    for (int i = 0; i < total_pixels * 3; i += 3) {
        // Apply basic color enhancement based on parameters
        float r = src[i + 0] / 255.0f;
        float g = src[i + 1] / 255.0f;
        float b = src[i + 2] / 255.0f;

        // Apply contrast and brightness adjustments
        r = fmaxf(0.0f, fminf(1.0f, r * param0 + param1));
        g = fmaxf(0.0f, fminf(1.0f, g * param0 + param1));
        b = fmaxf(0.0f, fminf(1.0f, b * param0 + param1));

        // Convert back to 0-255 range
        src[i + 0] = (unsigned char)(r * 255.0f);
        src[i + 1] = (unsigned char)(g * 255.0f);
        src[i + 2] = (unsigned char)(b * 255.0f);
    }
}

// Global color palette and index tables
// Spectra6 AIO display supports 6 colors: Black, White, Red, Green, Blue, Yellow
unsigned char spectra6_color_palette[6][3] = {
    {0x00, 0x00, 0x00},  // Black
    {0xFF, 0xFF, 0xFF},  // White
    {0xFF, 0x00, 0x00},  // Red
    {0x00, 0xFF, 0x00},  // Green
    {0x00, 0x00, 0xFF},  // Blue
    {0xFF, 0xFF, 0x00}   // Yellow
};

unsigned char spectra6_index_table[6] = {0, 1, 2, 3, 4, 5};

/*
 * IndexMapping_Spectra6_AIO_Y4 - Convert RGB888 to Y4 indexed format
 * @src: source RGB888 image data
 * @dst: destination Y4 image data
 * @width: image width
 * @height: image height
 */
void IndexMapping_Spectra6_AIO_Y4(unsigned char* src, unsigned char* dst, int width, int height) {
    // Initialize processing parameters
    float param0 = 1.5f;        // Base parameter
    float param1 = 0.05f;       // Color adjustment parameter  
    float param2 = 10.0f;       // Scaling parameter
    float param3 = 0.0f;        // Reserved parameter
    
    // Apply Spectra6 AIO image processing
    ImageProcess_Spectra6_AIO(src, width, height, param0, param1, param2, param3);
    
    int src_idx = 0;    // Source RGB index
    int dst_idx = 0;    // Destination Y4 index
    int total_pixels = width * height;
    
    // Process each pixel
    for (dst_idx = 0; dst_idx < total_pixels; dst_idx++) {
        // Extract RGB components from source
        unsigned char r = src[src_idx + 0];
        unsigned char g = src[src_idx + 1];
        unsigned char b = src[src_idx + 2];
        
        // Find best matching color in palette
        int best_color_index = 0;

        // Search through all 6 colors in the Spectra6 palette
        for (int color_idx = 0; color_idx <= 5; color_idx++) {
            // Compare with palette color
            if (r == spectra6_color_palette[color_idx][0] &&
                g == spectra6_color_palette[color_idx][1] &&
                b == spectra6_color_palette[color_idx][2]) {

                // Exact match found
                best_color_index = spectra6_index_table[color_idx];
                break;
            }
        }
        
        // Pack two 4-bit pixels into one byte for Y4 format
        if ((dst_idx & 1) == 0) {
            // Even pixel index - store in upper 4 bits
            int byte_idx = dst_idx / 2;
            dst[byte_idx] = (best_color_index << 4) & 0xFF;
        } else {
            // Odd pixel index - combine with lower 4 bits
            int byte_idx = dst_idx / 2;
            unsigned char existing_value = dst[byte_idx];
            dst[byte_idx] = (existing_value + (best_color_index & 0x0F)) & 0xFF;
        }
        
        src_idx += 3;  // Move to next RGB pixel
    }
}

/*
 * Alternative implementation with distance-based color matching
 * This version calculates Euclidean distance when exact match is not found
 */
void IndexMapping_Spectra6_AIO_Y4_Enhanced(unsigned char* src, unsigned char* dst, int width, int height) {
    // Initialize processing parameters
    float param0 = 1.5f;
    float param1 = 0.05f;
    float param2 = 10.0f;
    float param3 = 0.0f;
    
    // Apply Spectra6 AIO image processing
    ImageProcess_Spectra6_AIO(src, width, height, param0, param1, param2, param3);
    
    int src_idx = 0;
    int dst_idx = 0;
    int total_pixels = width * height;
    
    for (dst_idx = 0; dst_idx < total_pixels; dst_idx++) {
        unsigned char r = src[src_idx + 0];
        unsigned char g = src[src_idx + 1];
        unsigned char b = src[src_idx + 2];
        
        int best_color_index = 0;
        int min_distance = INT_MAX;
        
        // Find closest color using Euclidean distance
        for (int color_idx = 0; color_idx <= 5; color_idx++) {
            int dr = r - spectra6_color_palette[color_idx][0];
            int dg = g - spectra6_color_palette[color_idx][1];
            int db = b - spectra6_color_palette[color_idx][2];
            
            int distance = dr*dr + dg*dg + db*db;
            
            if (distance < min_distance) {
                min_distance = distance;
                best_color_index = spectra6_index_table[color_idx];
            }
        }
        
        // Pack into Y4 format
        if ((dst_idx & 1) == 0) {
            int byte_idx = dst_idx / 2;
            dst[byte_idx] = (best_color_index << 4) & 0xFF;
        } else {
            int byte_idx = dst_idx / 2;
            unsigned char existing_value = dst[byte_idx];
            dst[byte_idx] = (existing_value + (best_color_index & 0x0F)) & 0xFF;
        }
        
        src_idx += 3;
    }
}

/*
 * Example usage and test function
 */
void test_IndexMapping_Spectra6_AIO_Y4() {
    // Example usage
    int width = 128;
    int height = 64;
    
    // Allocate memory for RGB888 source (3 bytes per pixel)
    unsigned char* rgb_data = malloc(width * height * 3);
    
    // Allocate memory for Y4 destination (4 bits per pixel = 0.5 bytes per pixel)
    unsigned char* y4_data = malloc((width * height + 1) / 2);
    
    if (rgb_data && y4_data) {
        // Initialize with test pattern
        for (int i = 0; i < width * height * 3; i += 3) {
            rgb_data[i + 0] = (i / 3) % 256;  // R
            rgb_data[i + 1] = (i / 6) % 256;  // G  
            rgb_data[i + 2] = (i / 9) % 256;  // B
        }
        
        // Perform conversion
        IndexMapping_Spectra6_AIO_Y4(rgb_data, y4_data, width, height);
        
        printf("Conversion completed: %dx%d RGB888 -> Y4\n", width, height);
        printf("Input size: %d bytes\n", width * height * 3);
        printf("Output size: %d bytes\n", (width * height + 1) / 2);
    }
    
    // Clean up
    if (rgb_data) free(rgb_data);
    if (y4_data) free(y4_data);
}

/*
 * Utility function to print Y4 data in hex format
 */
void print_y4_data(unsigned char* y4_data, int pixel_count, int width) {
    printf("Y4 Data (hex):\n");
    int byte_count = (pixel_count + 1) / 2;

    for (int i = 0; i < byte_count; i++) {
        printf("%02X ", y4_data[i]);

        // Print newline every 16 bytes or at end of row
        if ((i + 1) % 16 == 0 || (i + 1) % ((width + 1) / 2) == 0) {
            printf("\n");
        }
    }
    printf("\n");
}

/*
 * Main function for testing
 */
int main() {
    printf("IndexMapping_Spectra6_AIO_Y4 Test Program\n");
    printf("=========================================\n\n");

    // Test with small image
    int width = 8;
    int height = 4;
    int total_pixels = width * height;

    // Create test RGB data
    unsigned char* rgb_data = malloc(total_pixels * 3);
    unsigned char* y4_data = malloc((total_pixels + 1) / 2);

    if (!rgb_data || !y4_data) {
        printf("Memory allocation failed!\n");
        return -1;
    }

    // Initialize with test pattern - create a gradient
    printf("Creating test RGB888 image (%dx%d):\n", width, height);
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int idx = (y * width + x) * 3;

            // Create a simple pattern
            rgb_data[idx + 0] = (x * 255) / (width - 1);   // R gradient
            rgb_data[idx + 1] = (y * 255) / (height - 1);  // G gradient
            rgb_data[idx + 2] = ((x + y) * 255) / (width + height - 2); // B gradient

            printf("(%3d,%3d,%3d) ", rgb_data[idx + 0], rgb_data[idx + 1], rgb_data[idx + 2]);
        }
        printf("\n");
    }
    printf("\n");

    // Perform conversion
    printf("Converting to Y4 format...\n");
    IndexMapping_Spectra6_AIO_Y4(rgb_data, y4_data, width, height);

    // Print results
    print_y4_data(y4_data, total_pixels, width);

    // Test enhanced version
    printf("Testing enhanced version with distance-based matching...\n");
    IndexMapping_Spectra6_AIO_Y4_Enhanced(rgb_data, y4_data, width, height);
    print_y4_data(y4_data, total_pixels, width);

    // Clean up
    free(rgb_data);
    free(y4_data);

    printf("Test completed successfully!\n");
    return 0;
}
