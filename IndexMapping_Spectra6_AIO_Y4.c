/*
 * IndexMapping_Spectra6_AIO_Y4 - C Implementation
 * Converted from ARM64 assembly code
 * 
 * This function performs color index mapping for Spectra6 AIO display
 * converting RGB888 input to Y4 (4-bit) indexed output
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <stdint.h>
#include <limits.h>

// Forward declarations
void ImageProcess_Spectra6_AIO(unsigned char* src, int width, int height,
                               float param0, float param1, float param2, float param3);

// External function prototypes (would be implemented elsewhere)
void ImageProcess_ColorEnhace(unsigned char* src, int width, int height,
                             float param0, float param1, float param2, float param3);
void ImageProcess_Dither_AIO(unsigned char* src, int width, int height);

/*
 * ImageProcess_Spectra6_AIO - Converted from ARM64 assembly
 * Assembly function: 00000000000028a4 <ImageProcess_Spectra6_AIO>
 *
 * This function performs Spectra6 AIO image processing by calling
 * two external functions: ImageProcess_ColorEnhace and ImageProcess_Dither_AIO
 */
void ImageProcess_Spectra6_AIO(unsigned char* src, int width, int height,
                               float param0, float param1, float param2, float param3) {

    // Assembly: 28a4: a9bd7bfd  stp x29, x30, [sp, #-48]!
    // Assembly: 28a8: 910003fd  mov x29, sp
    // Function prologue - save frame pointer and link register, set up stack frame

    // Assembly: 28ac: bd002fa0  str s0, [x29, #44]
    // Assembly: 28b0: bd002ba1  str s1, [x29, #40]
    // Assembly: 28b4: bd0027a2  str s2, [x29, #36]
    // Assembly: 28b8: bd0023a3  str s3, [x29, #32]
    // Store floating point parameters s0-s3 (param0-param3) to stack

    // Assembly: 28bc: f9000fa0  str x0, [x29, #24]
    // Assembly: 28c0: b90017a1  str w1, [x29, #20]
    // Assembly: 28c4: b90013a2  str w2, [x29, #16]
    // Store function parameters: x0=src, w1=width, w2=height to stack

    // Assembly: 28c8: b94013a2  ldr w2, [x29, #16]
    // Assembly: 28cc: b94017a1  ldr w1, [x29, #20]
    // Assembly: 28d0: f9400fa0  ldr x0, [x29, #24]
    // Load parameters for first function call: w2=height, w1=width, x0=src

    // Assembly: 28d4: bd4023a3  ldr s3, [x29, #32]
    // Assembly: 28d8: bd4027a2  ldr s2, [x29, #36]
    // Assembly: 28dc: bd402ba1  ldr s1, [x29, #40]
    // Assembly: 28e0: bd402fa0  ldr s0, [x29, #44]
    // Load floating point parameters: s3=param3, s2=param2, s1=param1, s0=param0

    // Assembly: 28e4: 97fff8ef  bl ca0 <ImageProcess_ColorEnhace@plt>
    // Call ImageProcess_ColorEnhace function
    ImageProcess_ColorEnhace(src, width, height, param0, param1, param2, param3);

    // Assembly: 28e8: b94013a2  ldr w2, [x29, #16]
    // Assembly: 28ec: b94017a1  ldr w1, [x29, #20]
    // Assembly: 28f0: f9400fa0  ldr x0, [x29, #24]
    // Load parameters for second function call: w2=height, w1=width, x0=src

    // Assembly: 28f4: 97fffe14  bl 2144 <ImageProcess_Dither_AIO>
    // Call ImageProcess_Dither_AIO function
    ImageProcess_Dither_AIO(src, width, height);

    // Assembly: 28f8: d503201f  nop
    // No operation (padding/alignment)

    // Assembly: 28fc: a8c37bfd  ldp x29, x30, [sp], #48
    // Assembly: 2900: d65f03c0  ret
    // Function epilogue - restore frame pointer and link register, return
}

/*
 * Simplified implementations of external functions for testing
 * These would normally be implemented elsewhere in the codebase
 */
void ImageProcess_ColorEnhace(unsigned char* src, int width, int height,
                             float param0, float param1, float param2, float param3) {
    int total_pixels = width * height;

    // Use param2 and param3 for additional processing if needed
    (void)param2;  // Suppress unused parameter warning
    (void)param3;  // Suppress unused parameter warning

    for (int i = 0; i < total_pixels * 3; i += 3) {
        // Apply basic color enhancement based on parameters
        float r = src[i + 0] / 255.0f;
        float g = src[i + 1] / 255.0f;
        float b = src[i + 2] / 255.0f;

        // Apply contrast and brightness adjustments
        r = fmaxf(0.0f, fminf(1.0f, r * param0 + param1));
        g = fmaxf(0.0f, fminf(1.0f, g * param0 + param1));
        b = fmaxf(0.0f, fminf(1.0f, b * param0 + param1));

        // Convert back to 0-255 range
        src[i + 0] = (unsigned char)(r * 255.0f);
        src[i + 1] = (unsigned char)(g * 255.0f);
        src[i + 2] = (unsigned char)(b * 255.0f);
    }
}

void ImageProcess_Dither_AIO(unsigned char* src, int width, int height) {
    // Simplified dithering implementation
    int total_pixels = width * height;

    for (int i = 0; i < total_pixels * 3; i += 3) {
        // Simple threshold dithering
        unsigned char r = src[i + 0];
        unsigned char g = src[i + 1];
        unsigned char b = src[i + 2];

        // Apply simple dithering pattern
        int pixel_idx = i / 3;
        int x = pixel_idx % width;
        int y = pixel_idx / width;

        // Bayer matrix 2x2 dithering
        int threshold = ((x & 1) ^ (y & 1)) ? 128 : 64;

        src[i + 0] = (r > threshold) ? 255 : 0;
        src[i + 1] = (g > threshold) ? 255 : 0;
        src[i + 2] = (b > threshold) ? 255 : 0;
    }
}

// Global color palette and index tables
// Spectra6 AIO display supports 6 colors: Black, White, Red, Green, Blue, Yellow
unsigned char spectra6_color_palette[6][3] = {
    {0x00, 0x00, 0x00},  // Black
    {0xFF, 0xFF, 0xFF},  // White
    {0xFF, 0x00, 0x00},  // Red
    {0x00, 0xFF, 0x00},  // Green
    {0x00, 0x00, 0xFF},  // Blue
    {0xFF, 0xFF, 0x00}   // Yellow
};

unsigned char spectra6_index_table[6] = {0, 1, 2, 3, 4, 5};

/*
 * IndexMapping_Spectra6_AIO_Y4 - Convert RGB888 to Y4 indexed format
 * Converted from ARM64 assembly: 0000000000002904 <IndexMapping_Spectra6_AIO_Y4>
 * @src: source RGB888 image data
 * @dst: destination Y4 image data
 * @width: image width
 * @height: image height
 */
void IndexMapping_Spectra6_AIO_Y4(unsigned char* src, unsigned char* dst, int width, int height) {
    // Assembly: 2904: a9ba7bfd  stp x29, x30, [sp, #-96]!
    // Assembly: 2908: 910003fd  mov x29, sp
    // Function prologue - save frame pointer and link register, allocate 96 bytes stack

    // Assembly: 290c: f90017a0  str x0, [x29, #40]
    // Assembly: 2910: f90013a1  str x1, [x29, #32]
    // Assembly: 2914: b9001fa2  str w2, [x29, #28]
    // Assembly: 2918: b9001ba3  str w3, [x29, #24]
    // Store function parameters: x0=src, x1=dst, w2=width, w3=height

    // Assembly: 291c: 1e2f1000  fmov s0, #1.500000000000000000e+00
    // Assembly: 2920: bd0053a0  str s0, [x29, #80]
    // Initialize param0 = 1.5f and store to stack
    float param0 = 1.5f;

    // Assembly: 2924: 529999a0  mov w0, #0xcccd
    // Assembly: 2928: 72a7a980  movk w0, #0x3d4c, lsl #16
    // Assembly: 292c: 1e270000  fmov s0, w0
    // Assembly: 2930: bd004fa0  str s0, [x29, #76]
    // Initialize param1 = 0.05f (0x3d4ccccd) and store to stack
    float param1 = 0.05f;

    // Assembly: 2934: 1e249000  fmov s0, #1.000000000000000000e+01
    // Assembly: 2938: bd004ba0  str s0, [x29, #72]
    // Initialize param2 = 10.0f and store to stack
    float param2 = 10.0f;

    // Assembly: 293c: b90047bf  str wzr, [x29, #68]
    // Initialize param3 = 0.0f and store to stack
    float param3 = 0.0f;

    // Assembly: 2940: b9401ba2  ldr w2, [x29, #24]
    // Assembly: 2944: b9401fa1  ldr w1, [x29, #28]
    // Assembly: 2948: f94017a0  ldr x0, [x29, #40]
    // Assembly: 294c: bd4047a3  ldr s3, [x29, #68]
    // Assembly: 2950: bd404ba2  ldr s2, [x29, #72]
    // Assembly: 2954: bd404fa1  ldr s1, [x29, #76]
    // Assembly: 2958: bd4053a0  ldr s0, [x29, #80]
    // Load parameters for ImageProcess_Spectra6_AIO call

    // Assembly: 295c: 97ffffd2  bl 28a4 <ImageProcess_Spectra6_AIO>
    // Call ImageProcess_Spectra6_AIO function
    ImageProcess_Spectra6_AIO(src, width, height, param0, param1, param2, param3);

    // Assembly: 2960: b9005fbf  str wzr, [x29, #92]
    // Assembly: 2964: b9005bbf  str wzr, [x29, #88]
    // Initialize src_idx = 0 and dst_idx = 0
    int src_idx = 0;    // [x29, #92]
    int dst_idx = 0;    // [x29, #88]

    // Assembly: 2968: 1400006a  b 2b10 <IndexMapping_Spectra6_AIO_Y4+0x20c>
    // Jump to loop condition check
    goto loop_condition;

loop_start:
    // Assembly: 296c: b9805fa0  ldrsw x0, [x29, #92]
    // Assembly: 2970: f94017a1  ldr x1, [x29, #40]
    // Assembly: 2974: 8b000020  add x0, x1, x0
    // Assembly: 2978: 39400000  ldrb w0, [x0]
    // Assembly: 297c: 39010fa0  strb w0, [x29, #67]
    // Load R component: r = src[src_idx + 0]
    unsigned char r = src[src_idx + 0];  // stored at [x29, #67]

    // Assembly: 2980: b9805fa0  ldrsw x0, [x29, #92]
    // Assembly: 2984: 91000400  add x0, x0, #0x1
    // Assembly: 2988: f94017a1  ldr x1, [x29, #40]
    // Assembly: 298c: 8b000020  add x0, x1, x0
    // Assembly: 2990: 39400000  ldrb w0, [x0]
    // Assembly: 2994: 39010ba0  strb w0, [x29, #66]
    // Load G component: g = src[src_idx + 1]
    unsigned char g = src[src_idx + 1];  // stored at [x29, #66]

    // Assembly: 2998: b9805fa0  ldrsw x0, [x29, #92]
    // Assembly: 299c: 91000800  add x0, x0, #0x2
    // Assembly: 29a0: f94017a1  ldr x1, [x29, #40]
    // Assembly: 29a4: 8b000020  add x0, x1, x0
    // Assembly: 29a8: 39400000  ldrb w0, [x0]
    // Assembly: 29ac: 390107a0  strb w0, [x29, #65]
    // Load B component: b = src[src_idx + 2]
    unsigned char b = src[src_idx + 2];  // stored at [x29, #65]

    // Assembly: 29b0: b90057bf  str wzr, [x29, #84]
    // Initialize color_idx = 0
    int color_idx = 0;  // [x29, #84]

    // Assembly: 29b4: 1400004e  b 2aec <IndexMapping_Spectra6_AIO_Y4+0x1e8>
    // Jump to inner loop condition
    goto color_loop_condition;

color_loop_start:
    // Assembly: 29b8: b0000080  adrp x0, 13000 <__FRAME_END__+0xf4b0>
    // Assembly: 29bc: f947e402  ldr x2, [x0, #4040]
    // Load color palette base address

    // Assembly: 29c0: b98057a1  ldrsw x1, [x29, #84]
    // Assembly: 29c4: aa0103e0  mov x0, x1
    // Assembly: 29c8: d37ff800  lsl x0, x0, #1
    // Assembly: 29cc: 8b010000  add x0, x0, x1
    // Assembly: 29d0: 8b000040  add x0, x2, x0
    // Calculate palette[color_idx] address: x0 = palette + color_idx * 3

    // Assembly: 29d4: 39400000  ldrb w0, [x0]
    // Assembly: 29d8: 39410fa1  ldrb w1, [x29, #67]
    // Assembly: 29dc: 6b00003f  cmp w1, w0
    // Assembly: 29e0: 54000801  b.ne 2ae0 <IndexMapping_Spectra6_AIO_Y4+0x1dc>
    // Compare r with palette[color_idx][0]
    if (r != spectra6_color_palette[color_idx][0]) goto color_loop_increment;

    // Assembly: 29e4: b0000080  adrp x0, 13000 <__FRAME_END__+0xf4b0>
    // Assembly: 29e8: f947e402  ldr x2, [x0, #4040]
    // Assembly: 29ec: b98057a1  ldrsw x1, [x29, #84]
    // Assembly: 29f0: aa0103e0  mov x0, x1
    // Assembly: 29f4: d37ff800  lsl x0, x0, #1
    // Assembly: 29f8: 8b010000  add x0, x0, x1
    // Assembly: 29fc: 8b000040  add x0, x2, x0
    // Assembly: 2a00: 39400400  ldrb w0, [x0, #1]
    // Assembly: 2a04: 39410ba1  ldrb w1, [x29, #66]
    // Assembly: 2a08: 6b00003f  cmp w1, w0
    // Assembly: 2a0c: 540006a1  b.ne 2ae0 <IndexMapping_Spectra6_AIO_Y4+0x1dc>
    // Compare g with palette[color_idx][1]
    if (g != spectra6_color_palette[color_idx][1]) goto color_loop_increment;

    // Assembly: 2a10: b0000080  adrp x0, 13000 <__FRAME_END__+0xf4b0>
    // Assembly: 2a14: f947e402  ldr x2, [x0, #4040]
    // Assembly: 2a18: b98057a1  ldrsw x1, [x29, #84]
    // Assembly: 2a1c: aa0103e0  mov x0, x1
    // Assembly: 2a20: d37ff800  lsl x0, x0, #1
    // Assembly: 2a24: 8b010000  add x0, x0, x1
    // Assembly: 2a28: 8b000040  add x0, x2, x0
    // Assembly: 2a2c: 39400800  ldrb w0, [x0, #2]
    // Assembly: 2a30: 394107a1  ldrb w1, [x29, #65]
    // Assembly: 2a34: 6b00003f  cmp w1, w0
    // Assembly: 2a38: 54000541  b.ne 2ae0 <IndexMapping_Spectra6_AIO_Y4+0x1dc>
    // Compare b with palette[color_idx][2]
    if (b != spectra6_color_palette[color_idx][2]) goto color_loop_increment;

    // Exact match found - get index from index table
    // Assembly: 2a3c: b0000080  adrp x0, 13000 <__FRAME_END__+0xf4b0>
    // Assembly: 2a40: f947e801  ldr x1, [x0, #4048]
    // Assembly: 2a44: b98057a0  ldrsw x0, [x29, #84]
    // Assembly: 2a48: 38606820  ldrb w0, [x1, x0]
    // Assembly: 2a4c: b9003fa0  str w0, [x29, #60]
    // Load best_color_index = index_table[color_idx]
    int best_color_index = spectra6_index_table[color_idx];  // [x29, #60]

    // Pack into Y4 format
    // Assembly: 2a50: b9405ba0  ldr w0, [x29, #88]
    // Assembly: 2a54: 12000000  and w0, w0, #0x1
    // Assembly: 2a58: 7100001f  cmp w0, #0x0
    // Assembly: 2a5c: 540001a1  b.ne 2a90 <IndexMapping_Spectra6_AIO_Y4+0x18c>
    // Check if dst_idx is even or odd
    if ((dst_idx & 1) == 0) {
        // Even pixel index - store in upper 4 bits
        // Assembly: 2a60: b9403fa0  ldr w0, [x29, #60]
        // Assembly: 2a64: 531c6c02  lsl w2, w0, #4
        // Assembly: 2a68: b9405ba0  ldr w0, [x29, #88]
        // Assembly: 2a6c: 531f7c01  lsr w1, w0, #31
        // Assembly: 2a70: 0b000020  add w0, w1, w0
        // Assembly: 2a74: 13017c00  asr w0, w0, #1
        // Assembly: 2a78: 93407c00  sxtw x0, w0
        // Assembly: 2a7c: f94013a1  ldr x1, [x29, #32]
        // Assembly: 2a80: 8b000020  add x0, x1, x0
        // Assembly: 2a84: 12001c41  and w1, w2, #0xff
        // Assembly: 2a88: 39000001  strb w1, [x0]
        int byte_idx = dst_idx / 2;
        dst[byte_idx] = (best_color_index << 4) & 0xFF;
        // Assembly: 2a8c: 1400001b  b 2af8 <IndexMapping_Spectra6_AIO_Y4+0x1f4>
        goto loop_increment;
    } else {
        // Odd pixel index - combine with lower 4 bits
        // Assembly: 2a90: b9405ba0  ldr w0, [x29, #88]
        // Assembly: 2a94: 531f7c01  lsr w1, w0, #31
        // Assembly: 2a98: 0b000020  add w0, w1, w0
        // Assembly: 2a9c: 13017c00  asr w0, w0, #1
        // Assembly: 2aa0: 2a0003e3  mov w3, w0
        // Assembly: 2aa4: 93407c60  sxtw x0, w3
        // Assembly: 2aa8: f94013a1  ldr x1, [x29, #32]
        // Assembly: 2aac: 8b000020  add x0, x1, x0
        // Assembly: 2ab0: 39400002  ldrb w2, [x0]
        int byte_idx = dst_idx / 2;
        unsigned char existing_value = dst[byte_idx];

        // Assembly: 2ab4: b9403fa0  ldr w0, [x29, #60]
        // Assembly: 2ab8: 12001c00  and w0, w0, #0xff
        // Assembly: 2abc: 12000c00  and w0, w0, #0xf
        // Assembly: 2ac0: 12001c01  and w1, w0, #0xff
        // Assembly: 2ac4: 93407c60  sxtw x0, w3
        // Assembly: 2ac8: f94013a3  ldr x3, [x29, #32]
        // Assembly: 2acc: 8b000060  add x0, x3, x0
        // Assembly: 2ad0: 0b010041  add w1, w2, w1
        // Assembly: 2ad4: 12001c21  and w1, w1, #0xff
        // Assembly: 2ad8: 39000001  strb w1, [x0]
        dst[byte_idx] = (existing_value + (best_color_index & 0x0F)) & 0xFF;
        // Assembly: 2adc: 14000007  b 2af8 <IndexMapping_Spectra6_AIO_Y4+0x1f4>
        goto loop_increment;
    }

color_loop_increment:
    // Assembly: 2ae0: b94057a0  ldr w0, [x29, #84]
    // Assembly: 2ae4: 11000400  add w0, w0, #0x1
    // Assembly: 2ae8: b90057a0  str w0, [x29, #84]
    // color_idx++
    color_idx++;

color_loop_condition:
    // Assembly: 2aec: b94057a0  ldr w0, [x29, #84]
    // Assembly: 2af0: 7100141f  cmp w0, #0x5
    // Assembly: 2af4: 54fff62d  b.le 29b8 <IndexMapping_Spectra6_AIO_Y4+0xb4>
    // Check if color_idx <= 5
    if (color_idx <= 5) goto color_loop_start;

loop_increment:
    // Assembly: 2af8: b9405fa0  ldr w0, [x29, #92]
    // Assembly: 2afc: 11000c00  add w0, w0, #0x3
    // Assembly: 2b00: b9005fa0  str w0, [x29, #92]
    // src_idx += 3
    src_idx += 3;

    // Assembly: 2b04: b9405ba0  ldr w0, [x29, #88]
    // Assembly: 2b08: 11000400  add w0, w0, #0x1
    // Assembly: 2b0c: b9005ba0  str w0, [x29, #88]
    // dst_idx++
    dst_idx++;

loop_condition:
    // Assembly: 2b10: b9401fa1  ldr w1, [x29, #28]
    // Assembly: 2b14: b9401ba0  ldr w0, [x29, #24]
    // Assembly: 2b18: 1b007c20  mul w0, w1, w0
    // Assembly: 2b1c: b9405ba1  ldr w1, [x29, #88]
    // Assembly: 2b20: 6b00003f  cmp w1, w0
    // Assembly: 2b24: 54fff24b  b.lt 296c <IndexMapping_Spectra6_AIO_Y4+0x68>
    // Check if dst_idx < width * height
    int total_pixels = width * height;
    if (dst_idx < total_pixels) goto loop_start;

    // Assembly: 2b28: d503201f  nop
    // Assembly: 2b2c: a8c67bfd  ldp x29, x30, [sp], #96
    // Assembly: 2b30: d65f03c0  ret
    // Function epilogue - restore registers and return
}

/*
 * Alternative implementation with distance-based color matching
 * This version calculates Euclidean distance when exact match is not found
 */
void IndexMapping_Spectra6_AIO_Y4_Enhanced(unsigned char* src, unsigned char* dst, int width, int height) {
    // Initialize processing parameters
    float param0 = 1.5f;
    float param1 = 0.05f;
    float param2 = 10.0f;
    float param3 = 0.0f;
    
    // Apply Spectra6 AIO image processing
    ImageProcess_Spectra6_AIO(src, width, height, param0, param1, param2, param3);
    
    int src_idx = 0;
    int dst_idx = 0;
    int total_pixels = width * height;
    
    for (dst_idx = 0; dst_idx < total_pixels; dst_idx++) {
        unsigned char r = src[src_idx + 0];
        unsigned char g = src[src_idx + 1];
        unsigned char b = src[src_idx + 2];
        
        int best_color_index = 0;
        int min_distance = INT_MAX;
        
        // Find closest color using Euclidean distance
        for (int color_idx = 0; color_idx <= 5; color_idx++) {
            int dr = r - spectra6_color_palette[color_idx][0];
            int dg = g - spectra6_color_palette[color_idx][1];
            int db = b - spectra6_color_palette[color_idx][2];
            
            int distance = dr*dr + dg*dg + db*db;
            
            if (distance < min_distance) {
                min_distance = distance;
                best_color_index = spectra6_index_table[color_idx];
            }
        }
        
        // Pack into Y4 format
        if ((dst_idx & 1) == 0) {
            int byte_idx = dst_idx / 2;
            dst[byte_idx] = (best_color_index << 4) & 0xFF;
        } else {
            int byte_idx = dst_idx / 2;
            unsigned char existing_value = dst[byte_idx];
            dst[byte_idx] = (existing_value + (best_color_index & 0x0F)) & 0xFF;
        }
        
        src_idx += 3;
    }
}

/*
 * Example usage and test function
 */
void test_IndexMapping_Spectra6_AIO_Y4() {
    // Example usage
    int width = 128;
    int height = 64;
    
    // Allocate memory for RGB888 source (3 bytes per pixel)
    unsigned char* rgb_data = malloc(width * height * 3);
    
    // Allocate memory for Y4 destination (4 bits per pixel = 0.5 bytes per pixel)
    unsigned char* y4_data = malloc((width * height + 1) / 2);
    
    if (rgb_data && y4_data) {
        // Initialize with test pattern
        for (int i = 0; i < width * height * 3; i += 3) {
            rgb_data[i + 0] = (i / 3) % 256;  // R
            rgb_data[i + 1] = (i / 6) % 256;  // G  
            rgb_data[i + 2] = (i / 9) % 256;  // B
        }
        
        // Perform conversion
        IndexMapping_Spectra6_AIO_Y4(rgb_data, y4_data, width, height);
        
        printf("Conversion completed: %dx%d RGB888 -> Y4\n", width, height);
        printf("Input size: %d bytes\n", width * height * 3);
        printf("Output size: %d bytes\n", (width * height + 1) / 2);
    }
    
    // Clean up
    if (rgb_data) free(rgb_data);
    if (y4_data) free(y4_data);
}

/*
 * Utility function to print Y4 data in hex format
 */
void print_y4_data(unsigned char* y4_data, int pixel_count, int width) {
    printf("Y4 Data (hex):\n");
    int byte_count = (pixel_count + 1) / 2;

    for (int i = 0; i < byte_count; i++) {
        printf("%02X ", y4_data[i]);

        // Print newline every 16 bytes or at end of row
        if ((i + 1) % 16 == 0 || (i + 1) % ((width + 1) / 2) == 0) {
            printf("\n");
        }
    }
    printf("\n");
}

/*
 * Main function for testing
 */
int main() {
    printf("IndexMapping_Spectra6_AIO_Y4 Test Program\n");
    printf("=========================================\n\n");

    // Test with small image
    int width = 8;
    int height = 4;
    int total_pixels = width * height;

    // Create test RGB data
    unsigned char* rgb_data = malloc(total_pixels * 3);
    unsigned char* y4_data = malloc((total_pixels + 1) / 2);

    if (!rgb_data || !y4_data) {
        printf("Memory allocation failed!\n");
        return -1;
    }

    // Initialize with test pattern - create a gradient
    printf("Creating test RGB888 image (%dx%d):\n", width, height);
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int idx = (y * width + x) * 3;

            // Create a simple pattern
            rgb_data[idx + 0] = (x * 255) / (width - 1);   // R gradient
            rgb_data[idx + 1] = (y * 255) / (height - 1);  // G gradient
            rgb_data[idx + 2] = ((x + y) * 255) / (width + height - 2); // B gradient

            printf("(%3d,%3d,%3d) ", rgb_data[idx + 0], rgb_data[idx + 1], rgb_data[idx + 2]);
        }
        printf("\n");
    }
    printf("\n");

    // Perform conversion
    printf("Converting to Y4 format...\n");
    IndexMapping_Spectra6_AIO_Y4(rgb_data, y4_data, width, height);

    // Print results
    print_y4_data(y4_data, total_pixels, width);

    // Test enhanced version
    printf("Testing enhanced version with distance-based matching...\n");
    IndexMapping_Spectra6_AIO_Y4_Enhanced(rgb_data, y4_data, width, height);
    print_y4_data(y4_data, total_pixels, width);

    // Clean up
    free(rgb_data);
    free(y4_data);

    printf("Test completed successfully!\n");
    return 0;
}
