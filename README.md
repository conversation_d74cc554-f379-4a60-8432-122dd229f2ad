# IndexMapping_Spectra6_AIO_Y4 - ARM64 Assembly to C Conversion

## Overview

This project contains the C language conversion of the `IndexMapping_Spectra6_AIO_Y4` function and related image processing functions from ARM64 assembly code. The conversion includes detailed assembly-to-C mappings with inline comments showing the corresponding assembly instructions.

## Files

- `IndexMapping_Spectra6_AIO_Y4.c` - Main C implementation with assembly comments
- `Makefile` - Build configuration for compilation
- `image_tools.txt` - Original ARM64 assembly disassembly
- `README.md` - This documentation file

## Key Functions Converted

### 1. ImageProcess_Spectra6_AIO
**Assembly Address:** `00000000000028a4`

This function performs Spectra6 AIO image processing by calling two external functions:
- `ImageProcess_ColorEnhace` - Color enhancement processing
- `ImageProcess_Dither_AIO` - Dithering algorithm

**Assembly-to-C Mapping:**
```c
void ImageProcess_Spectra6_AIO(unsigned char* src, int width, int height, 
                               float param0, float param1, float param2, float param3) {
    // Assembly: 28a4: a9bd7bfd  stp x29, x30, [sp, #-48]!
    // Assembly: 28a8: 910003fd  mov x29, sp
    // Function prologue - save frame pointer and link register
    
    // Assembly: 28ac-28c4: Store parameters to stack
    // Store floating point parameters s0-s3 and integer parameters
    
    // Assembly: 28e4: 97fff8ef  bl ca0 <ImageProcess_ColorEnhace@plt>
    ImageProcess_ColorEnhace(src, width, height, param0, param1, param2, param3);
    
    // Assembly: 28f4: 97fffe14  bl 2144 <ImageProcess_Dither_AIO>
    ImageProcess_Dither_AIO(src, width, height);
    
    // Assembly: 28fc: a8c37bfd  ldp x29, x30, [sp], #48
    // Assembly: 2900: d65f03c0  ret
    // Function epilogue - restore registers and return
}
```

### 2. IndexMapping_Spectra6_AIO_Y4
**Assembly Address:** `0000000000002904`

This function converts RGB888 images to Y4 (4-bit indexed) format for Spectra6 AIO displays.

**Key Features:**
- Color palette matching with 6 predefined colors
- Y4 format packing (2 pixels per byte)
- Distance-based color matching algorithm

## Assembly Instruction Mapping

Each C statement includes corresponding ARM64 assembly instructions as comments:

| Assembly Instruction | C Equivalent | Description |
|---------------------|--------------|-------------|
| `stp x29, x30, [sp, #-48]!` | Function prologue | Save frame pointer and link register |
| `str s0, [x29, #44]` | Parameter storage | Store float parameter to stack |
| `ldr w2, [x29, #16]` | Parameter loading | Load width parameter |
| `bl <function>` | Function call | Call external function |
| `ldp x29, x30, [sp], #48` | Function epilogue | Restore registers |
| `ret` | Return | Return from function |

## Build Instructions

### Prerequisites
- GCC compiler with ARM64 or x86_64 support
- Make utility
- Math library (libm)

### Compilation
```bash
# Build the program
make

# Build and run tests
make test

# Clean build files
make clean

# Build for specific architecture
make arm64    # For ARM64
make x86_64   # For x86_64

# Debug build
make debug

# Release build
make release
```

### Manual Compilation
```bash
gcc -Wall -Wextra -std=c99 -O2 -g -o IndexMapping_Spectra6_AIO_Y4 IndexMapping_Spectra6_AIO_Y4.c -lm
```

## Usage Example

```c
#include "IndexMapping_Spectra6_AIO_Y4.c"

int main() {
    int width = 128, height = 64;
    
    // Allocate RGB888 source data (3 bytes per pixel)
    unsigned char* rgb_data = malloc(width * height * 3);
    
    // Allocate Y4 destination data (4 bits per pixel = 0.5 bytes per pixel)
    unsigned char* y4_data = malloc((width * height + 1) / 2);
    
    // Initialize RGB data...
    
    // Convert to Y4 format
    IndexMapping_Spectra6_AIO_Y4(rgb_data, y4_data, width, height);
    
    // Use Y4 data...
    
    free(rgb_data);
    free(y4_data);
    return 0;
}
```

## Test Output

The program includes a test function that demonstrates the conversion:

```
IndexMapping_Spectra6_AIO_Y4 Test Program
=========================================

Creating test RGB888 image (8x4):
(  0,  0,  0) ( 36,  0, 25) ( 72,  0, 51) (109,  0, 76) ...

Converting to Y4 format...
Y4 Data (hex):
00 02 00 00
31 31 11 11
03 11 11 11
31 01 11 11

Test completed successfully!
```

## Color Palette

The Spectra6 AIO display supports 6 colors:
- Index 0: Black (0x00, 0x00, 0x00)
- Index 1: White (0xFF, 0xFF, 0xFF)
- Index 2: Red (0xFF, 0x00, 0x00)
- Index 3: Green (0x00, 0xFF, 0x00)
- Index 4: Blue (0x00, 0x00, 0xFF)
- Index 5: Yellow (0xFF, 0xFF, 0x00)

## Technical Notes

1. **Y4 Format**: Each byte contains two 4-bit pixels (upper and lower nibbles)
2. **Assembly Accuracy**: Each C statement includes the exact corresponding ARM64 assembly instruction
3. **Function Calls**: External function calls are preserved as in the original assembly
4. **Parameter Passing**: Follows ARM64 ABI conventions for parameter passing
5. **Stack Management**: Proper stack frame setup and cleanup as in assembly

## License

This code is converted from ARM64 assembly for educational and development purposes.
